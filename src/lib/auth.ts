import { betterAuth, } from 'better-auth'
import { drizzleAdapter } from 'better-auth/adapters/drizzle'
import { cors } from 'hono/cors'
// import Database from "better-sqlite3";
const db = null
export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: 'sqlite', // or "mysql", "sqlite"
  }),
})

export function getAuthInstance (database: D1Database) {
  return betterAuth({
    database: drizzleAdapter(database, {
      provider: 'sqlite', // or "mysql", "sqlite"
    }),
    emailAndPassword: {
      enabled: true,
    }
  })
}
