import { betterAuth } from 'better-auth'
import { drizzleAdapter } from 'better-auth/adapters/drizzle'
import { drizzle } from 'drizzle-orm/d1'
import * as schema from '@/db/schema/auth'
import { betterAuthOptions } from './options'

export const auth = (
  env: CloudflareBindings,
): ReturnType<typeof betterAuth> => {
  const db = drizzle(env.DB, { schema })

  return betterAuth({
    ...betterAuthOptions,
    database: drizzleAdapter(db, {
      provider: 'sqlite',
      schema: {
        account: schema.account,
        session: schema.session,
        user: schema.user,
        verification: schema.verification,
      },
    }),
    trustedOrigins: [env.BETTER_AUTH_URL, 'http://localhost:3000'],
  })
}
