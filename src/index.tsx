import { Hono } from 'hono'
import { renderer } from './renderer'
import posts from '@/routes/posts'
import customers from '@/routes/customer'
import { auth } from '@/lib/better-auth'
import { cors } from 'hono/cors'
import { Session, User } from 'better-auth'
import { whiteRoutes } from '@/constants'

const app = new Hono<{
  Bindings: CloudflareBindings
  // Variables: {
  //   user: User | null;
  //   session: Session | null
  // }
}>()

app.use(renderer)

// 为所有 /api/auth/* 路由添加 CORS 支持
app.use('/api/auth/*', cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'],
  allowHeaders: ['Content-Type', 'Authorization', 'Cookie', 'X-Requested-With'],
  allowMethods: ['POST', 'GET', 'OPTIONS', 'PUT', 'DELETE', 'PATCH'],
  exposeHeaders: ['Content-Length', 'Set-Cookie'],
  maxAge: 86400, // 24 hours
  credentials: true,
}))

// better-auth 处理器
app.on(['POST', 'GET'], '/api/auth/*', async (c) => {
  console.log('better-auth 处理器')
  const response = await auth(c.env).handler(c.req.raw)


  // 确保响应包含正确的 CORS 头部
  response.headers.set('Access-Control-Allow-Credentials', 'true')
  const origin = c.req.header('Origin')
  if (origin && ['http://localhost:3000', 'http://localhost:5173'].includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin)
  }

  return response
})

app.use('*', async (c, next) => {
  console.log('请求路径', c.req.path)
  // 排除白名单路径的 session 校验
  if (whiteRoutes.includes(c.req.path)) {
    return next()
  }

  const session = await auth(c.env).api.getSession({ headers: c.req.raw.headers })
  console.log('session', session)
  if (!session) {
    c.set('user', null)
    c.set('session', null)
    c.status(401)
    return c.json({
      code: 401,
      message: '未登录'
    })
    // return next()
  }

  c.set('user', session.user)
  c.set('session', session.session)
  return next()
})
app.get('/', (c) => {
  return c.render(<h1>Hello!</h1>)
})
app.route('/api', posts)
app.route('/api', customers)

export default app
