import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { whiteRoutes } from '@/constants'
import { auth } from '@/lib/better-auth'
import customers from '@/routes/customer'
import posts from '@/routes/posts'
import type { HonoVariables } from '@/types/global'
import { renderer } from './renderer'

const app = new Hono<{
  Bindings: CloudflareBindings
  Variables: HonoVariables
}>()

app.use(renderer)

// 为所有 /api/auth/* 路由添加 CORS 支持
app.use('*', async (c, next) => {
  const corsMiddlewareHandler = cors({
    allowHeaders: ['Content-Type', 'Authorization'],
    allowMethods: ['POST', 'GET', 'OPTIONS'],
    credentials: true,
    exposeHeaders: ['Content-Length'],
    maxAge: 600,
    origin: [c.env.BETTER_AUTH_URL, 'http://localhost:3000'],
  })
  return corsMiddlewareHandler(c, next)
})

// better-auth 处理器
app.on(['POST', 'GET'], '/api/auth/*', async (c) => {
  const response = await auth(c.env).handler(c.req.raw)

  // 确保响应包含正确的 CORS 头部
  response.headers.set('Access-Control-Allow-Credentials', 'true')
  return response
})

app.use('*', async (c, next) => {
  // 排除白名单路径的 session 校验
  if (whiteRoutes.includes(c.req.path)) {
    return next()
  }

  const session = await auth(c.env).api.getSession({
    headers: c.req.raw.headers,
  })
  if (!session) {
    c.set('user', null)
    c.set('session', null)
    c.status(401)
    return c.json({
      code: 401,
      message: '未登录',
    })
  }

  c.set('user', session.user)
  c.set('session', session.session)
  return next()
})
app.get('/', (c) => {
  return c.render(<h1>Hello!</h1>)
})
app.route('/api', posts)
app.route('/api', customers)

export default app
