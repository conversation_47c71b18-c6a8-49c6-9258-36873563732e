import { Hono } from 'hono'
import { renderer } from './renderer'
import posts from '@/routes/posts'
import customers from '@/routes/customer'
import { auth } from '@/lib/better-auth'
import { cors } from 'hono/cors'
import { Session, User } from 'better-auth'
import { whiteRoutes } from '@/constants'

const app = new Hono<{
  Bindings: CloudflareBindings
  Variables: {
    user: User | null;
    session: Session | null
  }
}>()

app.use('/api/auth/*', cors({
  origin: 'http://localhost:3000', // replace with your origin
  allowHeaders: ['Content-Type', 'Authorization'],
  allowMethods: ['POST', 'GET', 'OPTIONS'],
  exposeHeaders: ['Content-Length'],
  maxAge: 600,
  credentials: true,
}))
app.use(renderer)
app.on(['POST', 'GET'], '/api/auth/*', (c) => {
  return auth(c.env).handler(c.req.raw)
})

app.use('*', async (c, next) => {
  console.log('请求路径', c.req.path)
  // 排除白名单路径的 session 校验
  if (whiteRoutes.includes(c.req.path)) {
    return next()
  }

  const session = await auth(c.env).api.getSession({ headers: c.req.raw.headers })
  console.log('session', session)
  if (!session) {
    c.set('user', null)
    c.set('session', null)
    c.status(401)
    return c.json({
      code: 401,
      message: '未登录'
    })
    // return next()
  }

  c.set('user', session.user)
  c.set('session', session.session)
  return next()
})
app.get('/', (c) => {
  return c.render(<h1>Hello!</h1>)
})
app.route('/api', posts)
app.route('/api', customers)

export default app
