import { Hono } from 'hono'
import { renderer } from './renderer'
import posts from '@/routes/posts'
import customers from '@/routes/customer'
import { getAuthInstance } from '@/lib/auth'
import { cors } from 'hono/cors'
import { Session, User } from 'better-auth'

const app = new Hono<{
  Bindings: CloudflareBindings
  Variables: {
    user: User | null;
    session: Session | null
  }
}>()

app.use(renderer)
app.use('/api/auth/**', cors({
  origin: 'http://localhost:3000', // replace with your origin
  allowHeaders: ['Content-Type', 'Authorization'],
  allowMethods: ['POST', 'GET', 'OPTIONS'],
  exposeHeaders: ['Content-Length'],
  maxAge: 600,
  credentials: true,
}))
app.on(['POST', 'GET'], '/api/auth/**', (c) => {
  const auth = getAuthInstance(c.env.DB)
  return auth.handler(c.req.raw)
})

app.use('*', async (c, next) => {
  const auth = getAuthInstance(c.env.DB)
  const session = await auth.api.getSession({ headers: c.req.raw.headers })

  if (!session) {
    c.set('user', null)
    c.set('session', null)
    return next()
  }

  c.set('user', session.user)
  c.set('session', session.session)
  return next()
})
app.get('/', (c) => {
  return c.render(<h1>Hello!</h1>)
})
app.route('/api', posts)
app.route('/api', customers)

export default app
