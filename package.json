{"name": "hono-cloudflare-workers", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "$npm_execpath run build && vite preview", "deploy": "$npm_execpath run build && wrangler deploy", "cf-typegen": "wrangler types --env-interface CloudflareBindings"}, "dependencies": {"hono": "^4.9.2"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.11.5", "vite": "^7.1.2", "vite-ssr-components": "^0.5.0", "wrangler": "^4.30.0"}}