{"name": "hono-cloudflare-workers", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "$npm_execpath run build && vite preview", "deploy": "$npm_execpath run build && wrangler deploy", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"better-auth": "^1.3.6", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "hono": "^4.9.2"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.11.5", "@nerdfolio/drizzle-d1-helpers": "^0.1.4", "@types/bun": "^1.2.20", "drizzle-kit": "^0.31.4", "vite": "^7.1.2", "vite-ssr-components": "^0.5.0", "wrangler": "^4.30.0"}}