# hono-cloudflare-workers

基于Hono+Cloudflare+D1数据库的项目模板

## 技术栈

- [Hono](https://hono.dev/)
- [Cloudflare Workers](https://workers.cloudflare.com/)
- [D1数据库](https://developers.cloudflare.com/d1/)

## 启动项目

```txt
npm install
npm run dev
```

## 部署到 Cloudflare Workers

```txt
npm run deploy
```

## 生成 Cloudflare Workers 类型

[For generating/synchronizing types based on your Worker configuration run](https://developers.cloudflare.com/workers/wrangler/commands/#types):

```txt
npm run cf-typegen
```

Pass the `CloudflareBindings` as generics when instantiation `Hono`:

```ts
// src/index.ts
const app = new Hono<{ Bindings: CloudflareBindings }>()
```

## 创建D1数据库

```shell
wrangler d1 create hono-cloudflare-workers-d1
```

## 数据库生成

```shell
bun db:generate
```

## 生成本地数据库

运行上面的命令生产数据库后会在 `dirzzle` 目录下生成一个 `.sql` 文件，文件内容是数据库的生成语句，
继续运行下面的脚本生成本地的 D1 数据库

```shell
wrangler d1 execute hono-cloudflare-workers-d1 --local --file=./drizzle/<filename>.sql
```

## 数据库同步至 Cloudflare D1

```shell
bun db:push
```
